@echo off
echo ========================================
echo     DEPLOY BACKEND - HEROKU
echo ========================================
echo.

echo [1/5] Verificando Heroku CLI...
heroku --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Heroku CLI nao encontrado!
    echo Instale em: https://devcenter.heroku.com/articles/heroku-cli
    pause
    exit /b 1
)

echo Heroku CLI encontrado!
echo.

echo [2/5] Fazendo login no Heroku...
heroku login

echo.
echo [3/5] Criando app Heroku...
cd backend

heroku create promandato-backend-api --region us

echo.
echo [4/5] Configurando variaveis de ambiente...
heroku config:set NODE_ENV=production
heroku config:set PORT=3002

echo.
echo [5/5] Fazendo deploy...
git init 2>nul
git add .
git commit -m "Deploy backend to Heroku" 2>nul || git commit --amend -m "Deploy backend to Heroku"
git push heroku main

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo       BACKEND DEPLOYADO COM SUCESSO!
    echo ========================================
    echo.
    echo URL do Backend: 
    heroku info -s | findstr web_url
    echo.
    echo Comandos uteis:
    echo - Ver logs: heroku logs --tail
    echo - Abrir app: heroku open
    echo - Configurar vars: heroku config:set VAR=value
    echo.
) else (
    echo.
    echo ERRO: Deploy falhou!
    echo Tente: heroku logs --tail
)

cd ..
pause
