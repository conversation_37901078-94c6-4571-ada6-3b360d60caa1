import fs from 'fs-extra';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import config from '../config.js';
import path from 'path';

class User {
  constructor(data) {
    this.id = data.id || uuidv4();
    this.email = data.email;
    this.password = data.password; // Já deve estar hasheada
    this.name = data.name;
    this.role = data.role || 'USER';
    this.permissions = data.permissions || [];
    this.planId = data.planId || null;
    this.isActive = data.isActive !== undefined ? data.isActive : true;
    this.emailVerified = data.emailVerified || false;
    this.twoFactorEnabled = data.twoFactorEnabled || false;
    this.twoFactorSecret = data.twoFactorSecret || null;
    this.loginAttempts = data.loginAttempts || 0;
    this.lockUntil = data.lockUntil || null;
    this.lastLogin = data.lastLogin || null;
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
    this.profile = data.profile || {
      avatar: null,
      phone: null,
      department: null,
      position: null
    };
  }

  // Métodos estáticos para gerenciar usuários
  static async loadUsers() {
    try {
      await fs.ensureFile(config.database.usersPath);
      const data = await fs.readFile(config.database.usersPath, 'utf8');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      return [];
    }
  }

  static async saveUsers(users) {
    try {
      await fs.ensureDir(path.dirname(config.database.usersPath));
      await fs.writeFile(config.database.usersPath, JSON.stringify(users, null, 2));
      return true;
    } catch (error) {
      console.error('Erro ao salvar usuários:', error);
      return false;
    }
  }

  static async findById(id) {
    const users = await this.loadUsers();
    const userData = users.find(user => user.id === id);
    return userData ? new User(userData) : null;
  }

  static async findByEmail(email) {
    const users = await this.loadUsers();
    const userData = users.find(user => user.email.toLowerCase() === email.toLowerCase());
    return userData ? new User(userData) : null;
  }

  static async findAll(filters = {}) {
    const users = await this.loadUsers();
    let filteredUsers = users;

    if (filters.role) {
      filteredUsers = filteredUsers.filter(user => user.role === filters.role);
    }

    if (filters.isActive !== undefined) {
      filteredUsers = filteredUsers.filter(user => user.isActive === filters.isActive);
    }

    if (filters.search) {
      const search = filters.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search)
      );
    }

    return filteredUsers.map(userData => new User(userData));
  }

  static async create(userData) {
    const users = await this.loadUsers();
    
    // Verificar se o email já existe
    const existingUser = users.find(user => user.email.toLowerCase() === userData.email.toLowerCase());
    if (existingUser) {
      throw new Error('Email já está em uso');
    }

    // Hash da senha
    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, config.security.saltRounds);
    }

    const newUser = new User(userData);
    users.push(newUser);
    
    const saved = await this.saveUsers(users);
    if (!saved) {
      throw new Error('Erro ao salvar usuário');
    }

    return newUser;
  }

  static async hashPassword(password) {
    return await bcrypt.hash(password, config.security.saltRounds);
  }

  static async comparePassword(password, hashedPassword) {
    return await bcrypt.compare(password, hashedPassword);
  }

  // Métodos de instância
  async save() {
    const users = await User.loadUsers();
    const index = users.findIndex(user => user.id === this.id);
    
    this.updatedAt = new Date().toISOString();
    
    if (index !== -1) {
      users[index] = this.toJSON();
    } else {
      users.push(this.toJSON());
    }
    
    const saved = await User.saveUsers(users);
    if (!saved) {
      throw new Error('Erro ao salvar usuário');
    }
    
    return this;
  }

  async delete() {
    const users = await User.loadUsers();
    const filteredUsers = users.filter(user => user.id !== this.id);
    return await User.saveUsers(filteredUsers);
  }

  async validatePassword(password) {
    return await bcrypt.compare(password, this.password);
  }

  async updatePassword(newPassword) {
    this.password = await bcrypt.hash(newPassword, config.security.saltRounds);
    this.updatedAt = new Date().toISOString();
    return await this.save();
  }

  async incrementLoginAttempts() {
    this.loginAttempts += 1;
    
    // Se excedeu o limite, bloquear por um tempo
    if (this.loginAttempts >= config.security.maxLoginAttempts) {
      this.lockUntil = new Date(Date.now() + config.security.lockoutDuration).toISOString();
    }
    
    return await this.save();
  }

  async resetLoginAttempts() {
    this.loginAttempts = 0;
    this.lockUntil = null;
    this.lastLogin = new Date().toISOString();
    return await this.save();
  }

  isLocked() {
    return this.lockUntil && new Date(this.lockUntil) > new Date();
  }

  hasPermission(permission) {
    // Admin tem todas as permissões
    if (this.role === 'ADMIN' || this.permissions.includes('*')) {
      return true;
    }

    // Verificar permissões específicas
    return this.permissions.includes(permission);
  }

  getRolePermissions() {
    const roleConfig = config.roles[this.role];
    return roleConfig ? roleConfig.permissions : [];
  }

  toJSON() {
    return {
      id: this.id,
      email: this.email,
      password: this.password,
      name: this.name,
      role: this.role,
      permissions: this.permissions,
      planId: this.planId,
      isActive: this.isActive,
      emailVerified: this.emailVerified,
      twoFactorEnabled: this.twoFactorEnabled,
      twoFactorSecret: this.twoFactorSecret,
      loginAttempts: this.loginAttempts,
      lockUntil: this.lockUntil,
      lastLogin: this.lastLogin,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      profile: this.profile
    };
  }

  toSafeJSON() {
    const { password, twoFactorSecret, ...safeData } = this.toJSON();
    return safeData;
  }
}

export default User;