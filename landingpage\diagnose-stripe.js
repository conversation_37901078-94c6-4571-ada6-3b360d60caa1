/**
 * Script de diagnóstico para verificar a integração Stripe
 */

(async function diagnoseStripeIntegration() {
    console.log('🔍 Iniciando diagnóstico da integração Stripe...\n');

    // Função para detectar URL da API
    function getApiUrl() {
        if (window.location.port === '5500' || window.location.hostname === '127.0.0.1') {
            return 'http://localhost:3002';
        }
        if (window.location.port === '3002') {
            return window.location.origin;
        }
        return 'http://localhost:3002';
    }

    const API_URL = getApiUrl();
    const STRIPE_PUBLIC_KEY = 'pk_live_51QUu2mClUIoqY19kQholKzLBzhCKuYnrCqGAQPtJL3vfvp3BcuyhGopNqirFP9DzOcp1GMMPnoHCibwMIOGBroQq00Frc8LxxN';

    console.log('📋 Configuração detectada:');
    console.log(`  - URL atual: ${window.location.href}`);
    console.log(`  - API URL: ${API_URL}`);
    console.log(`  - Stripe Key: ${STRIPE_PUBLIC_KEY.substring(0, 20)}...`);
    console.log('');

    // 1. Testar Stripe
    console.log('🔧 Testando Stripe...');
    try {
        if (typeof Stripe === 'undefined') {
            throw new Error('Stripe não carregado');
        }
        
        const stripe = Stripe(STRIPE_PUBLIC_KEY);
        console.log('  ✅ Stripe inicializado com sucesso');
    } catch (error) {
        console.log(`  ❌ Erro no Stripe: ${error.message}`);
    }

    // 2. Testar conexão com backend
    console.log('\n🌐 Testando conexão com backend...');
    try {
        const response = await fetch(`${API_URL}/api/health`);
        if (response.ok) {
            const data = await response.json();
            console.log(`  ✅ Backend conectado: ${data.message}`);
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        console.log(`  ❌ Erro no backend: ${error.message}`);
    }

    // 3. Testar endpoint de checkout
    console.log('\n💳 Testando endpoint de checkout...');
    try {
        const testData = {
            planType: 'basic',
            billingCycle: 'monthly',
            leadData: {
                name: 'Teste',
                email: '<EMAIL>',
                source: 'diagnostic'
            },
            successUrl: `${window.location.origin}/success.html`,
            cancelUrl: `${window.location.origin}/index.html`
        };

        const response = await fetch(`${API_URL}/api/stripe/create-checkout-session`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });

        if (response.ok) {
            const session = await response.json();
            console.log(`  ✅ Checkout session criada: ${session.sessionId}`);
        } else {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
    } catch (error) {
        console.log(`  ❌ Erro no checkout: ${error.message}`);
    }

    // 4. Verificar elementos da página
    console.log('\n🎯 Verificando elementos da página...');
    
    const planButtons = document.querySelectorAll('.plan-button[data-plan]');
    console.log(`  - Botões de plano encontrados: ${planButtons.length}`);
    
    const billingToggles = document.querySelectorAll('.billing-toggle');
    console.log(`  - Toggles de billing encontrados: ${billingToggles.length}`);
    
    const priceElements = document.querySelectorAll('#basic-price, #standard-price, #professional-price');
    console.log(`  - Elementos de preço encontrados: ${priceElements.length}`);

    // 5. Testar StripeIntegration class
    console.log('\n🔌 Verificando StripeIntegration...');
    if (typeof window.StripeIntegration !== 'undefined') {
        console.log('  ✅ Classe StripeIntegration disponível');
        
        if (window.StripeIntegration.stripe) {
            console.log('  ✅ Stripe instance inicializada');
        } else {
            console.log('  ⚠️  Stripe instance não inicializada');
        }
    } else {
        console.log('  ❌ Classe StripeIntegration não encontrada');
    }

    console.log('\n✅ Diagnóstico completo!');
    console.log('\n💡 Para testar manualmente:');
    console.log('  1. Abra o console do navegador');
    console.log('  2. Execute: diagnoseStripeIntegration()');
    console.log('  3. Clique nos botões de plano para testar');

})();

// Exportar para uso manual
window.diagnoseStripeIntegration = arguments.callee;
