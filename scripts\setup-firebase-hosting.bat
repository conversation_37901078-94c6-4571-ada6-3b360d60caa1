@echo off
echo ========================================
echo    ProMandato Firebase Hosting Setup
echo ========================================
echo.

echo [1/6] Verificando Firebase CLI...
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI nao encontrado. Instalando...
    npm install -g firebase-tools
) else (
    echo Firebase CLI encontrado!
)

echo.
echo [2/6] Fazendo login no Firebase...
firebase login

echo.
echo [3/6] Configurando projeto Firebase...
firebase use --add

echo.
echo [4/6] Gerando token para GitHub Actions...
echo.
echo IMPORTANTE: Copie o token que aparecera e configure no GitHub!
echo GitHub > Settings > Secrets > FIREBASE_TOKEN
echo.
pause
firebase login:ci

echo.
echo [5/6] Testando configuracao localmente...
echo Iniciando servidor local na porta 5000...
echo Acesse: http://localhost:5000
echo Pressione Ctrl+C para parar o servidor
firebase serve --only hosting

echo.
echo [6/6] Setup concluido!
echo.
echo Proximos passos:
echo 1. Configure o secret FIREBASE_TOKEN no GitHub
echo 2. Faca o primeiro deploy: firebase deploy --only hosting
echo 3. Teste o deploy automatico fazendo um commit
echo.
pause
