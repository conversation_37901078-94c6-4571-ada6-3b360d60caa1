import nodemailer from 'nodemailer';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Inicializar transportador de email
   */
  initializeTransporter() {
    // Configuração para desenvolvimento (usar Ethereal Email ou SMTP real)
    if (process.env.NODE_ENV === 'production') {
      // Configuração para produção (ex: SendGrid, AWS SES, etc.)
      this.transporter = nodemailer.createTransporter({
        service: process.env.EMAIL_SERVICE || 'gmail',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });
    } else {
      // Para desenvolvimento, simular envio de email
      this.transporter = {
        sendMail: async (mailOptions) => {
          console.log('📧 Email simulado enviado:');
          console.log('Para:', mailOptions.to);
          console.log('Assunto:', mailOptions.subject);
          console.log('Conteúdo:', mailOptions.text || 'HTML content');
          
          // Salvar email em arquivo para debug
          await this.saveEmailToFile(mailOptions);
          
          return { messageId: `fake-${Date.now()}` };
        }
      };
    }
  }

  /**
   * Enviar email
   * @param {Object} options - Opções do email
   * @returns {Promise<Object>} - Resultado do envio
   */
  async sendEmail(options) {
    try {
      const mailOptions = {
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      console.log(`Email enviado para ${options.to}: ${result.messageId}`);
      return {
        success: true,
        messageId: result.messageId
      };

    } catch (error) {
      console.error('Erro ao enviar email:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Salvar email em arquivo para debug
   * @param {Object} mailOptions - Opções do email
   */
  async saveEmailToFile(mailOptions) {
    try {
      const emailsPath = path.join(__dirname, '../data/emails');
      await fs.ensureDir(emailsPath);
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `email-${timestamp}.html`;
      const filePath = path.join(emailsPath, filename);
      
      const emailContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${mailOptions.subject}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .email-header { background: #f5f5f5; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        .email-content { background: white; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="email-header">
        <h3>Email Debug - ${new Date().toLocaleString('pt-BR')}</h3>
        <p><strong>Para:</strong> ${mailOptions.to}</p>
        <p><strong>Assunto:</strong> ${mailOptions.subject}</p>
    </div>
    <div class="email-content">
        ${mailOptions.html || `<pre>${mailOptions.text}</pre>`}
    </div>
</body>
</html>
      `;
      
      await fs.writeFile(filePath, emailContent);
      console.log(`Email salvo em: ${filePath}`);
      
    } catch (error) {
      console.error('Erro ao salvar email:', error);
    }
  }

  /**
   * Enviar email de boas-vindas
   * @param {Object} user - Dados do usuário
   * @param {string} tempPassword - Senha temporária
   * @param {string} planType - Tipo de plano
   */
  async sendWelcomeEmail(user, tempPassword, planType) {
    const planNames = {
      basic: 'Básico',
      standard: 'Padrão',
      professional: 'Profissional'
    };

    const subject = 'Bem-vindo ao Pro-Mandato - Conta Criada!';
    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
        .credentials { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .steps { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; color: #666; font-size: 14px; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Bem-vindo ao Pro-Mandato!</h1>
        </div>
        
        <div class="content">
            <p>Olá <strong>${user.name}</strong>,</p>
            <p>Seu pagamento foi processado com sucesso e sua conta foi criada! Agora você tem acesso completo ao Pro-Mandato.</p>
            
            <div class="credentials">
                <h3>📋 Dados de Acesso:</h3>
                <p><strong>Email:</strong> ${user.email}</p>
                <p><strong>Senha Temporária:</strong> <code style="background: #f3f4f6; padding: 4px 8px; border-radius: 4px;">${tempPassword}</code></p>
                <p><strong>Plano:</strong> ${planNames[planType] || planType}</p>
            </div>
            
            <div class="steps">
                <h3>🚀 Próximos Passos:</h3>
                <ol>
                    <li>Acesse o sistema clicando no botão abaixo</li>
                    <li>Faça login com suas credenciais</li>
                    <li><strong>Altere sua senha</strong> na primeira vez</li>
                    <li>Configure seu perfil e equipe</li>
                    <li>Explore os recursos do seu plano</li>
                </ol>
            </div>
            
            <div style="text-align: center;">
                <a href="${process.env.FRONTEND_URL || 'http://localhost:5174'}" class="button">
                    Acessar Pro-Mandato
                </a>
            </div>
            
            <p><strong>⚠️ Importante:</strong> Por segurança, altere sua senha temporária no primeiro acesso.</p>
            
            <p>Se precisar de ajuda ou tiver dúvidas, nossa equipe está pronta para ajudar!</p>
        </div>
        
        <div class="footer">
            <p>Equipe Pro-Mandato<br>
            <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>
    `;

    return await this.sendEmail({
      to: user.email,
      subject,
      html
    });
  }

  /**
   * Enviar email de recuperação de senha
   * @param {Object} user - Dados do usuário
   * @param {string} resetToken - Token de reset
   */
  async sendPasswordResetEmail(user, resetToken) {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/reset-password?token=${resetToken}`;
    
    const subject = 'Recuperação de Senha - Pro-Mandato';
    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Recuperação de Senha</h1>
        </div>
        
        <div class="content">
            <p>Olá <strong>${user.name}</strong>,</p>
            <p>Recebemos uma solicitação para redefinir a senha da sua conta no Pro-Mandato.</p>
            
            <div style="text-align: center;">
                <a href="${resetUrl}" class="button">
                    Redefinir Senha
                </a>
            </div>
            
            <div class="warning">
                <p><strong>⚠️ Importante:</strong></p>
                <ul>
                    <li>Este link expira em 1 hora</li>
                    <li>Se você não solicitou esta alteração, ignore este email</li>
                    <li>Nunca compartilhe este link com outras pessoas</li>
                </ul>
            </div>
            
            <p>Se o botão não funcionar, copie e cole este link no seu navegador:</p>
            <p style="word-break: break-all; background: #f3f4f6; padding: 10px; border-radius: 4px;">
                ${resetUrl}
            </p>
        </div>
    </div>
</body>
</html>
    `;

    return await this.sendEmail({
      to: user.email,
      subject,
      html
    });
  }
}

// Exportar instância única
const emailService = new EmailService();

export const sendEmail = (options) => emailService.sendEmail(options);
export const sendWelcomeEmail = (user, tempPassword, planType) => emailService.sendWelcomeEmail(user, tempPassword, planType);
export const sendPasswordResetEmail = (user, resetToken) => emailService.sendPasswordResetEmail(user, resetToken);

export default emailService;
