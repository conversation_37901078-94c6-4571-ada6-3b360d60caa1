import { User } from '../types/User';

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    accessToken: string;
    refreshToken: string;
  };
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

class AuthService {
  private baseURL: string;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    // Usar import.meta.env para Vite em vez de process.env
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';
    this.loadTokensFromStorage();
  }

  private loadTokensFromStorage(): void {
    this.accessToken = localStorage.getItem('accessToken');
    this.refreshToken = localStorage.getItem('refreshToken');
  }

  private saveTokensToStorage(accessToken: string, refreshToken: string): void {
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
  }

  private clearTokensFromStorage(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    this.accessToken = null;
    this.refreshToken = null;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.accessToken) {
      defaultHeaders['Authorization'] = `Bearer ${this.accessToken}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      // Se o token expirou, tentar renovar
      if (response.status === 401 && data.code === 'TOKEN_EXPIRED' && this.refreshToken) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          // Tentar novamente com o novo token
          config.headers = {
            ...config.headers,
            'Authorization': `Bearer ${this.accessToken}`,
          };
          const retryResponse = await fetch(url, config);
          return await retryResponse.json();
        }
      }

      return data;
    } catch (error) {
      console.error('Erro na requisição:', error);
      throw new Error('Erro de conexão com o servidor');
    }
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await this.makeRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });

      if (response.success && response.data) {
        this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }

      return response;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  async logout(): Promise<void> {
    try {
      if (this.accessToken) {
        await this.makeRequest('/auth/logout', {
          method: 'POST',
        });
      }
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    } finally {
      this.clearTokensFromStorage();
    }
  }

  async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) {
      return false;
    }

    try {
      const response = await this.makeRequest('/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({ refreshToken: this.refreshToken }),
      });

      if (response.success && response.data) {
        this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Erro ao renovar token:', error);
      this.clearTokensFromStorage();
      return false;
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        return JSON.parse(storedUser);
      }

      if (!this.accessToken) {
        return null;
      }

      const response = await this.makeRequest('/auth/me');
      
      if (response.success && response.data) {
        localStorage.setItem('user', JSON.stringify(response.data.user));
        return response.data.user;
      }

      return null;
    } catch (error) {
      console.error('Erro ao obter usuário atual:', error);
      return null;
    }
  }

  async updateProfile(profileData: { 
    name?: string; 
    email?: string;
    currentPassword?: string;
    newPassword?: string;
    profile?: { 
      phone?: string; 
      department?: string; 
      position?: string; 
    } 
  }): Promise<User> {
    try {
      const response = await this.makeRequest('/auth/profile', {
        method: 'PUT',
        body: JSON.stringify(profileData),
      });

      if (response.success && response.data) {
        localStorage.setItem('user', JSON.stringify(response.data.user));
        return response.data.user;
      }

      throw new Error(response.message || 'Erro ao atualizar perfil');
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Erro desconhecido');
    }
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<AuthResponse> {
    try {
      const response = await this.makeRequest('/auth/change-password', {
        method: 'POST',
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      });

      return response;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }

  hasPermission(permission: string): boolean {
    try {
      const storedUser = localStorage.getItem('user');
      if (!storedUser) return false;

      const user: User = JSON.parse(storedUser);
      
      // Admin tem todas as permissões
      if (user.role === 'ADMIN' || user.permissions.includes('*')) {
        return true;
      }

      return user.permissions.includes(permission);
    } catch (error) {
      console.error('Erro ao verificar permissão:', error);
      return false;
    }
  }

  hasRole(role: string | string[]): boolean {
    try {
      const storedUser = localStorage.getItem('user');
      if (!storedUser) return false;

      const user: User = JSON.parse(storedUser);
      const roles = Array.isArray(role) ? role : [role];
      
      return roles.includes(user.role);
    } catch (error) {
      console.error('Erro ao verificar role:', error);
      return false;
    }
  }
}

// Instância singleton
const authService = new AuthService();

export default authService;
export type { LoginCredentials, User, AuthResponse };
