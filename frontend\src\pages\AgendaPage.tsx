
import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { Input, Textarea } from '../components/ui/Input';
import { ICONS } from '../constants';
import { AgendaEvent } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { useAuth } from '../hooks/useAuth';
import { getAgendaEvents, addAgendaEvent, updateAgendaEvent, deleteAgendaEvent } from '../services/firebaseService';

const AgendaPage: React.FC = () => {
  const [events, setEvents] = useState<AgendaEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentEvent, setCurrentEvent] = useState<Partial<AgendaEvent> | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const { currentUser } = useAuth();

  const fetchEvents = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedEvents = await getAgendaEvents();
      setEvents(fetchedEvents);
    } catch (err) {
      console.error("Failed to fetch agenda events:", err);
      setError("Falha ao carregar eventos da agenda. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  const handleOpenModal = (event?: AgendaEvent) => {
    const formatDateTimeLocal = (isoString?: string): string => {
        if (!isoString) return new Date(Date.now() + (event ? 0 : 60*60*1000)).toISOString().substring(0, 16);
        try {
            return new Date(isoString).toISOString().substring(0, 16);
        } catch { 
            return new Date(Date.now() + (event ? 0 : 60*60*1000)).toISOString().substring(0, 16);
        }
    };

    const defaultStartTime = formatDateTimeLocal(event?.start);
    const defaultEndTime = formatDateTimeLocal(event?.end || new Date(new Date(defaultStartTime).getTime() + 60*60*1000).toISOString());


    setCurrentEvent(event ? 
        {...event, start: formatDateTimeLocal(event.start), end: formatDateTimeLocal(event.end)} 
        : 
        { title: '', start: defaultStartTime, end: defaultEndTime, isAllDay: false }
    );
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentEvent(null);
    setError(null);
  };

  const handleSaveEvent = async () => {
    if (!currentEvent || !currentUser) {
      setError("Dados do evento ou usuário inválidos.");
      return;
    }
    if (!currentEvent.title || !currentEvent.start || !currentEvent.end) {
        setError("Título, Início e Fim são obrigatórios.");
        return;
    }
    if (new Date(currentEvent.start) >= new Date(currentEvent.end)) {
        setError("A data de término deve ser posterior à data de início.");
        return;
    }

    setIsSaving(true);
    setError(null);
    try {
      const eventDataToSave = {
        title: currentEvent.title!,
        start: new Date(currentEvent.start!).toISOString(), 
        end: new Date(currentEvent.end!).toISOString(),     
        description: currentEvent.description,
        attendees: currentEvent.attendees,
        location: currentEvent.location,
        isAllDay: currentEvent.isAllDay || false,
      };

      if (currentEvent.id) { 
        await updateAgendaEvent(currentEvent.id, eventDataToSave as Omit<AgendaEvent, 'id' | 'createdAt' | 'createdBy' | 'updatedAt'>);
      } else { 
        await addAgendaEvent(eventDataToSave as Omit<AgendaEvent, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, currentUser.id);
      }
      await fetchEvents(); 
      handleCloseModal();
    } catch (err) {
      console.error("Failed to save event:", err);
      setError("Falha ao salvar evento. Tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleDeleteEvent = async (id: string) => {
    if (window.confirm("Tem certeza que deseja excluir este evento?")) {
      setIsLoading(true);
      setError(null);
      try {
        await deleteAgendaEvent(id);
        await fetchEvents();
      } catch (err) {
        console.error("Failed to delete event:", err);
        setError("Falha ao excluir evento. Tente novamente.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const formatDateDisplay = (dateString?: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleString('pt-BR', { dateStyle: 'short', timeStyle: 'short'});
    } catch {
      return "Data inválida";
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Agenda</h1>
        <Button onClick={() => handleOpenModal()} leftIcon={ICONS.PLUS}>
          Novo Evento
        </Button>
      </div>

      {error && !isModalOpen && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-3 rounded-md">{error}</p>}
      
      <Card title="Próximos Eventos">
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <ul className="divide-y divide-gray-200 dark:divide-neutral-dark">
            {events.length > 0 ? events.sort((a,b) => new Date(a.start).getTime() - new Date(b.start).getTime()).map(event => (
              <li key={event.id} className="py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-lg font-semibold text-primary dark:text-primary-light">{event.title}</p>
                    <p className="text-sm text-gray-600 dark:text-neutral-DEFAULT">
                      Início: {formatDateDisplay(event.start)} | Fim: {formatDateDisplay(event.end)}
                    </p>
                    {event.location && <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT mt-1">Local: {event.location}</p>}
                    {event.description && <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT mt-1">{event.description}</p>}
                  </div>
                  <div className="space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => handleOpenModal(event)} className="text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary">
                      {React.cloneElement(ICONS.PENCIL, { className: 'w-5 h-5'})}
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteEvent(event.id)} className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-500">
                      {React.cloneElement(ICONS.TRASH, { className: 'w-5 h-5'})}
                    </Button>
                  </div>
                </div>
              </li>
            )) : (
              <li>
                <p className="text-gray-500 dark:text-neutral-DEFAULT py-4 text-center">Nenhum evento agendado.</p>
              </li>
            )}
          </ul>
        )}
      </Card>
      
      <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT">
        Nota: Esta é uma visualização simplificada da agenda. Para uma experiência completa com visualização diária, semanal e mensal, 
        seria necessário integrar uma biblioteca de calendário como FullCalendar.
      </p>

      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={currentEvent?.id ? "Editar Evento" : "Novo Evento"} size="lg">
        <div className="space-y-4">
          {error && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-2 rounded-md text-sm">{error}</p>}
          <Input
            label="Título do Evento"
            value={currentEvent?.title || ''}
            onChange={(e) => setCurrentEvent(prev => ({ ...prev, title: e.target.value }))}
            required
            disabled={isSaving}
          />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Início"
              type="datetime-local"
              value={currentEvent?.start || ''}
              onChange={(e) => setCurrentEvent(prev => ({ ...prev, start: e.target.value }))}
              required
              disabled={isSaving}
            />
            <Input
              label="Fim"
              type="datetime-local"
              value={currentEvent?.end || ''}
              onChange={(e) => setCurrentEvent(prev => ({ ...prev, end: e.target.value }))}
              required
              disabled={isSaving}
            />
          </div>
          <Input
            label="Local (Opcional)"
            value={currentEvent?.location || ''}
            onChange={(e) => setCurrentEvent(prev => ({ ...prev, location: e.target.value }))}
            disabled={isSaving}
          />
          <Textarea
            label="Descrição (Opcional)"
            value={currentEvent?.description || ''}
            onChange={(e) => setCurrentEvent(prev => ({ ...prev, description: e.target.value }))}
            disabled={isSaving}
          />
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={handleCloseModal} disabled={isSaving}>Cancelar</Button>
            <Button onClick={handleSaveEvent} isLoading={isSaving} disabled={isSaving}>
                {currentEvent?.id ? "Salvar Alterações" : "Criar Evento"}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AgendaPage;