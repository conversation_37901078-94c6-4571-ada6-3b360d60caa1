@echo off
echo ========================================
echo     ProMandato Landing Page Deploy
echo ========================================
echo.

echo Verificando arquivos da landing page...
if not exist "landingpage\index.html" (
    echo ERRO: Arquivo landingpage\index.html nao encontrado!
    pause
    exit /b 1
)

echo Arquivos encontrados!
echo.

echo Iniciando deploy para Firebase Hosting...
firebase deploy --only hosting

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo          DEPLOY CONCLUIDO!
    echo ========================================
    echo.
    echo Sua landing page esta disponivel em:
    echo https://promandato-landing.web.app
    echo.
    echo Para configurar dominio customizado:
    echo 1. Acesse Firebase Console
    echo 2. Va para Hosting
    echo 3. Clique em "Adicionar dominio personalizado"
    echo.
) else (
    echo.
    echo ERRO: Deploy falhou!
    echo Verifique sua conexao e tente novamente.
    echo.
)

pause
