# Deploy automático para Firebase Hosting
name: Deploy to Firebase Hosting on merge
'on':
  push:
    branches:
      - main
    paths:
      - 'landingpage/**'
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Deploy to Firebase Hosting
        run: |
          echo "Deploying to Firebase Hosting..."
          firebase deploy --only hosting --project promandato-9a4cf --token "${{ secrets.FIREBASE_TOKEN }}"
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
